#!/usr/bin/env bash

# -----------------------------------------------------------------------------
# 时光印记项目前端部署脚本（优化版）
# 用法:
#   ./deploy_frontend.sh -h
#   ./deploy_frontend.sh -i SERVER_IP [-u USERNAME] [-p SSH_PORT] [-k SSH_KEY] \
#       --frontend-dir FRONT_DIR --backend-dir BACK_DIR
#
# 示例:
#   ./deploy_frontend.sh -i ************* -u ubuntu -p 2222 -k ~/.ssh/id_rsa \
#       --frontend-dir shiguangyinji_frontend-algorithm \
#       --backend-dir shiguangyinji_backend-algorithm
# -----------------------------------------------------------------------------

set -euo pipefail

# 颜色输出
RED='\033[0;31m';   GREEN='\033[0;32m'
YELLOW='\033[1;33m'; BLUE='\033[0;34m'
NC='\033[0m'

usage() {
  cat << EOF
Usage: $0 -i SERVER_IP [-u USERNAME] [-p SSH_PORT] [-k SSH_KEY]
          --frontend-dir FRONT_DIR --backend-dir BACK_DIR

  -i, --ip           服务器 IP 地址（必填）
  -u, --user         SSH 登录用户名，默认 root
  -p, --port         SSH 端口，默认 22
  -k, --key          私钥文件路径，可选
      --frontend-dir 本地前端项目目录（必填）
      --backend-dir  远程后端项目目录名，用于 Nginx 配置（必填）
  -h, --help         显示本帮助信息并退出
EOF
  exit 1
}

# 默认值
REMOTE_USER="root"
SSH_PORT=22
SSH_KEY=""
PROJECT_NAME="shiguangyinji"
PROJECT_ROOT="/root/${PROJECT_NAME}"

# 解析参数
while (( "$#" )); do
  case "$1" in
    -i|--ip)          SERVER_IP="$2"; shift 2 ;;
    -u|--user)        REMOTE_USER="$2"; shift 2 ;;
    -p|--port)        SSH_PORT="$2"; shift 2 ;;
    -k|--key)         SSH_KEY="$2"; shift 2 ;;
    --frontend-dir)   FRONTEND_DIR="$2"; shift 2 ;;
    --backend-dir)    BACKEND_DIR="$2"; shift 2 ;;
    -h|--help)        usage ;;
    *) echo -e "${RED}未知参数: $1${NC}"; usage ;;
  esac
done

# 校验必填
if [[ -z "${SERVER_IP:-}" || -z "${FRONTEND_DIR:-}" || -z "${BACKEND_DIR:-}" ]]; then
  echo -e "${RED}错误：缺少必填参数${NC}"
  usage
fi

SSH_OPTS="-p ${SSH_PORT}"
[[ -n "$SSH_KEY" ]] && SSH_OPTS+=" -i ${SSH_KEY}"

echo -e "${GREEN}部署参数：${NC}"
echo -e "  服务器 IP: ${SERVER_IP}"
echo -e "  用户:      ${REMOTE_USER}"
echo -e "  SSH 端口:  ${SSH_PORT}"
[[ -n "$SSH_KEY" ]] && echo -e "  SSH Key:   ${SSH_KEY}"
echo -e "  前端目录:  ${FRONTEND_DIR}"
echo -e "  后端目录:  ${BACKEND_DIR}"
echo

# 1. 本地检查
echo -e "${YELLOW}1. 检查本地前端项目...${NC}"
if [[ ! -d "$FRONTEND_DIR" ]]; then
  echo -e "${RED}错误: 前端目录不存在：$FRONTEND_DIR${NC}"
  exit 1
fi
for file in package.json vite.config.js; do
  if [[ ! -f "$FRONTEND_DIR/$file" ]]; then
    echo -e "${RED}错误: 缺少 $file${NC}"
    exit 1
  fi
done

# 2. 在服务器上创建项目目录
echo -e "${YELLOW}2. 创建远程项目目录...${NC}"
ssh $SSH_OPTS ${REMOTE_USER}@${SERVER_IP} "sudo mkdir -p ${PROJECT_ROOT}"

# 3. 上传前端代码（排除 node_modules）
echo -e "${YELLOW}3. 上传前端代码（排除 node_modules）...${NC}"
rsync -avz -e "ssh $SSH_OPTS" \
  --exclude 'node_modules' \
  --exclude '.git' \
  "$FRONTEND_DIR" \
  ${REMOTE_USER}@${SERVER_IP}:${PROJECT_ROOT}/

# 4. 远程构建与部署
echo -e "${YELLOW}4. 远程构建与部署前端...${NC}"
ssh $SSH_OPTS ${REMOTE_USER}@${SERVER_IP} bash << EOF
set -euo pipefail

cd ${PROJECT_ROOT}/$(basename $FRONTEND_DIR)

# 安装 Node.js（如果需要）
if ! command -v node &> /dev/null; then
  echo "安装 Node.js..."
  curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
  sudo apt-get update
  sudo apt-get install -y nodejs
fi

echo "Node.js \$(node --version), npm \$(npm --version)"

# 安装依赖 & 构建
npm install
echo ">> 安装 terser..."
npm install --save-dev terser
cat > .env.production << EOF2
VITE_API_BASE_URL=http://${SERVER_IP}:8000/api/
VITE_APP_TITLE="时光印记"
EOF2

npm run build

# 验证构建产物
if [[ ! -d dist ]]; then
  echo "错误: 构建失败，未找到 dist/ 目录"
  exit 1
fi

# 调整权限
sudo chown -R www-data:www-data ${PROJECT_ROOT}
EOF

# 5. 配置 Nginx
echo -e "${YELLOW}5. 配置 Nginx...${NC}"
ssh $SSH_OPTS ${REMOTE_USER}@${SERVER_IP} bash << EOF
set -euo pipefail

# 安装 Nginx（如果需要）
if ! command -v nginx &> /dev/null; then
  sudo apt-get update
  sudo apt-get install -y nginx
fi

# 写入 Nginx 配置
sudo tee /etc/nginx/sites-available/${PROJECT_NAME} > /dev/null << 'NGX'
server {
    listen 80;
    server_name ${SERVER_IP};

    # 前端静态资源
    location / {
        root ${PROJECT_ROOT}/$(basename $FRONTEND_DIR)/dist;
        try_files \$uri \$uri/ /index.html;

        location ~* \\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff2?|ttf|eot)\$ {
            expires 365d;
            add_header Cache-Control "public, immutable";
        }
    }

    # 后端 API
    location /api/ {
        proxy_pass http://127.0.0.1:8000/api/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout    60s;
        proxy_read_timeout    60s;
    }

    # 媒体与静态文件
    location /media/ {
        alias ${PROJECT_ROOT}/${BACKEND_DIR}/media/;
        expires 365d;
        add_header Cache-Control "public";
    }
    location /static/ {
        alias ${PROJECT_ROOT}/${BACKEND_DIR}/staticfiles/;
        expires 365d;
        add_header Cache-Control "public";
    }

    # 安全头与压缩
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/json;
}
NGX

# 启用并重启
sudo ln -sf /etc/nginx/sites-available/${PROJECT_NAME} /etc/nginx/sites-enabled/${PROJECT_NAME}
sudo nginx -t
sudo systemctl restart nginx
EOF

# 6. 验证部署
echo -e "${YELLOW}6. 验证部署...${NC}"
ssh $SSH_OPTS ${REMOTE_USER}@${SERVER_IP} bash << EOF
set -euo pipefail

echo "Nginx 状态："
sudo systemctl status nginx --no-pager -l

echo
echo "检查 dist/ 目录："
if [[ -d "${PROJECT_ROOT}/$(basename $FRONTEND_DIR)/dist" ]]; then
  echo "✓ dist/ 存在，文件数：$(find "${PROJECT_ROOT}/$(basename $FRONTEND_DIR)/dist" -type f | wc -l)"
else
  echo "✗ dist/ 不存在"
  exit 1
fi

echo
echo "检查 80 端口监听："
if ss -tln | grep -q ':80'; then
  echo "✓ 80 端口正在监听"
else
  echo "✗ 80 端口未监听"
fi
EOF

echo -e "${GREEN}部署完成！ 请访问: http://${SERVER_IP}${NC}"
echo -e "${BLUE}常用运维命令：${NC}"
echo -e "  查看 Nginx 状态: ssh $SSH_OPTS ${REMOTE_USER}@${SERVER_IP} 'sudo systemctl status nginx'"
echo -e "  查看 Nginx 日志: ssh $SSH_OPTS ${REMOTE_USER}@${SERVER_IP} 'sudo tail -f /var/log/nginx/access.log'"
echo -e "  重启 Nginx:      ssh $SSH_OPTS ${REMOTE_USER}@${SERVER_IP} 'sudo systemctl restart nginx'"
echo -e "  列出构建文件:   ssh $SSH_OPTS ${REMOTE_USER}@${SERVER_IP} 'ls -la ${PROJECT_ROOT}/\$(basename $FRONTEND_DIR)/dist'"
