# 时光印记项目远程部署适配文档

## 概述
本文档详细说明了时光印记项目从本地开发环境迁移到远程服务器的适配工作和部署步骤。

## 一、前端适配修改

### 1.1 修改的文件

#### 1.1.1 `shiguangyinji_frontend-algorithm/src/utils/request.js`
**修改内容：**
- 添加了环境变量支持的API基础URL配置
- 修改了token刷新时的API地址配置
- 支持开发环境和生产环境的自动切换

**具体修改：**
```javascript
// 新增：根据环境变量确定API基础URL
const getBaseURL = () => {
  // 优先使用环境变量配置
  if (import.meta.env.VITE_API_BASE_URL) {
    return import.meta.env.VITE_API_BASE_URL
  }
  
  // 开发环境使用本地地址，生产环境使用远程服务器地址
  if (import.meta.env.DEV) {
    return 'http://127.0.0.1:8000/api/'
  } else {
    // 生产环境使用远程服务器地址，需要替换为实际的服务器IP
    return 'http://*************:8000/api/'
  }
}

// 修改：axios配置使用动态baseURL
const service = axios.create({
  baseURL: getBaseURL(),
  timeout: 100000
})

// 修改：token刷新也使用动态baseURL
const res = await axios.post(getBaseURL() + 'token/refresh/', {
  refresh: refreshToken
})
```

#### 1.1.2 `shiguangyinji_frontend-algorithm/vite.config.js`
**修改内容：**
- 添加了生产环境构建配置
- 配置了资源压缩和优化选项
- 移除了生产环境的console和debugger

**具体修改：**
```javascript
build: {
  // 生产环境构建配置
  outDir: 'dist',
  assetsDir: 'assets',
  // 生成sourcemap用于调试
  sourcemap: false,
  // 压缩配置
  minify: 'terser',
  terserOptions: {
    compress: {
      drop_console: true, // 移除console
      drop_debugger: true // 移除debugger
    }
  }
}
```

### 1.2 环境变量配置

需要在项目根目录创建以下环境变量文件：

#### `.env.development` (开发环境)
```
VITE_API_BASE_URL=http://127.0.0.1:8000/api/
VITE_APP_TITLE=时光印记(开发版)
```

#### `.env.production` (生产环境)
```
VITE_API_BASE_URL=http://*************:8000/api/
VITE_APP_TITLE=时光印记
```

**注意：** 需要将 `*************` 替换为实际的服务器IP地址。

## 二、后端适配修改

### 2.1 修改的文件

#### 2.1.1 `shiguangyinji_backend-algorithm/utils/edit.py`
**修改内容：**
- 保留了原有的 `upload_file` 函数（用于Gitee上传）
- 新增了 `upload_file_to_server` 函数（用于服务器本地文件管理）
- 新增了 `get_file_url` 函数（智能选择上传方式）
- 修改了文件上传调用逻辑

**新增函数说明：**

1. **`upload_file_to_server` 函数**
   - 功能：将文件复制到服务器本地存储目录
   - 返回：HTTP格式的文件访问URL
   - 特点：适用于远程服务器部署，不依赖外部服务

2. **`get_file_url` 函数**
   - 功能：根据部署环境智能选择文件上传方式
   - 开发环境：使用Gitee上传
   - 生产环境：使用服务器本地文件管理
   - 自动检测环境并选择合适的处理方式

**修改的调用位置：**
- `generate_conclusion` 函数中的数字人生成部分
- `generate_conclusion` 函数中的视频生成部分

### 2.2 配置要求

需要在Django设置中添加生产环境标识：

```python
# settings.py
IS_PRODUCTION = True  # 生产环境设置为True
```

## 三、远程部署步骤

### 3.1 服务器环境准备

1. **安装必要软件**
   ```bash
   # 更新系统
   sudo apt update && sudo apt upgrade -y
   
   # 安装Python、Node.js、Nginx等
   sudo apt install python3 python3-pip nodejs npm nginx -y
   
   # 安装MySQL（如果使用MySQL数据库）
   sudo apt install mysql-server -y
   ```

2. **配置防火墙**
   ```bash
   # 开放必要端口
   sudo ufw allow 22    # SSH
   sudo ufw allow 80    # HTTP
   sudo ufw allow 443   # HTTPS
   sudo ufw allow 8000  # Django开发服务器
   sudo ufw allow 3306  # MySQL
   ```

### 3.2 数据库配置

1. **连接远程数据库**
   - 数据库地址：xxx.com
   - 端口：3306
   - 使用提供的数据库账号密码

2. **修改Django数据库配置**
   ```python
   # settings.py
   DATABASES = {
       'default': {
           'ENGINE': 'django.db.backends.mysql',
           'NAME': 'your_database_name',
           'USER': 'your_username',
           'PASSWORD': 'your_password',
           'HOST': 'xxx.com',
           'PORT': '3306',
       }
   }
   ```

### 3.3 后端部署

1. **上传后端代码到服务器**
   ```bash
   # 使用scp或git克隆
   scp -r shiguangyinji_backend-algorithm/ user@*************:/path/to/project/
   ```

2. **安装Python依赖**
   ```bash
   cd /path/to/project/shiguangyinji_backend-algorithm
   pip3 install -r requirements.txt
   ```

3. **配置环境变量**
   ```bash
   # 设置Django环境变量
   export DJANGO_SETTINGS_MODULE=shiguangyinji.settings
   export IS_PRODUCTION=True
   ```

4. **数据库迁移**
   ```bash
   python3 manage.py makemigrations
   python3 manage.py migrate
   ```

5. **收集静态文件**
   ```bash
   python3 manage.py collectstatic
   ```

6. **启动Django服务**
   ```bash
   # 开发模式启动
   python3 manage.py runserver 0.0.0.0:8000
   
   # 或使用Gunicorn（推荐生产环境）
   pip3 install gunicorn
   gunicorn --bind 0.0.0.0:8000 shiguangyinji.wsgi:application
   ```

### 3.4 前端部署

1. **上传前端代码到服务器**
   ```bash
   scp -r shiguangyinji_frontend-algorithm/ user@*************:/path/to/project/
   ```

2. **安装Node.js依赖**
   ```bash
   cd /path/to/project/shiguangyinji_frontend-algorithm
   npm install
   ```

3. **配置环境变量**
   ```bash
   # 创建生产环境配置文件
   echo "VITE_API_BASE_URL=http://*************:8000/api/" > .env.production
   ```

4. **构建生产版本**
   ```bash
   npm run build
   ```

5. **配置Nginx**
   ```nginx
   # /etc/nginx/sites-available/shiguangyinji
   server {
       listen 80;
       server_name *************;
       
       # 前端静态文件
       location / {
           root /path/to/project/shiguangyinji_frontend-algorithm/dist;
           try_files $uri $uri/ /index.html;
       }
       
       # 后端API代理
       location /api/ {
           proxy_pass http://127.0.0.1:8000/api/;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       }
       
       # 媒体文件
       location /media/ {
           alias /path/to/project/shiguangyinji_backend-algorithm/media/;
       }
   }
   ```

6. **启用Nginx配置**
   ```bash
   sudo ln -s /etc/nginx/sites-available/shiguangyinji /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl restart nginx
   ```

### 3.5 服务管理

1. **使用systemd管理服务**
   ```bash
   # 创建Django服务文件
   sudo nano /etc/systemd/system/shiguangyinji-django.service
   ```

   ```ini
   [Unit]
   Description=Shiguangyinji Django Application
   After=network.target
   
   [Service]
   User=www-data
   Group=www-data
   WorkingDirectory=/path/to/project/shiguangyinji_backend-algorithm
   Environment="PATH=/path/to/project/venv/bin"
   ExecStart=/path/to/project/venv/bin/gunicorn --bind 0.0.0.0:8000 shiguangyinji.wsgi:application
   Restart=always
   
   [Install]
   WantedBy=multi-user.target
   ```

2. **启动服务**
   ```bash
   sudo systemctl enable shiguangyinji-django
   sudo systemctl start shiguangyinji-django
   sudo systemctl status shiguangyinji-django
   ```

## 四、注意事项

### 4.1 安全配置
- 修改默认的数据库密码
- 配置SSL证书（推荐使用Let's Encrypt）
- 定期更新系统和依赖包
- 配置防火墙规则

### 4.2 性能优化
- 使用Redis缓存（可选）
- 配置数据库连接池
- 启用Gzip压缩
- 配置CDN（可选）

### 4.3 监控和日志
- 配置日志轮转
- 设置监控告警
- 定期备份数据

### 4.4 故障排查
- 检查服务状态：`sudo systemctl status service-name`
- 查看日志：`sudo journalctl -u service-name -f`
- 检查端口占用：`netstat -tlnp | grep :8000`

## 五、验证部署

1. **访问前端页面**：`http://*************`
2. **测试API接口**：`http://*************/api/`
3. **检查文件上传功能**
4. **验证数据库连接**

## 六、回滚方案

如果部署出现问题，可以快速回滚：

1. **恢复数据库备份**
2. **切换回本地配置**
3. **重启服务**

---

**文档版本：** v1.0  
**更新时间：** 2024年12月  
**维护人员：** 开发团队 