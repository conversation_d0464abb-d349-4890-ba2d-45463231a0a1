# 时光印记项目部署检查清单

## 部署前准备

### ✅ 服务器环境检查
- [ ] 服务器操作系统：Ubuntu 20.04+ 或 CentOS 7+
- [ ] 内存：至少 2GB RAM
- [ ] 存储：至少 10GB 可用空间
- [ ] 网络：稳定的互联网连接

### ✅ 服务器软件安装
- [ ] Python 3.8+
- [ ] Node.js 14+
- [ ] Nginx
- [ ] MySQL 客户端
- [ ] Git

### ✅ 防火墙配置
- [ ] 开放端口 22 (SSH)
- [ ] 开放端口 80 (HTTP)
- [ ] 开放端口 443 (HTTPS)
- [ ] 开放端口 8000 (Django)
- [ ] 开放端口 3306 (MySQL，如果需要)

## 数据库配置

### ✅ 远程数据库连接
- [ ] 数据库地址：xxx.com
- [ ] 端口：3306
- [ ] 数据库名称
- [ ] 用户名
- [ ] 密码
- [ ] 测试数据库连接

### ✅ Django数据库配置
- [ ] 修改 `settings.py` 中的数据库配置
- [ ] 设置 `IS_PRODUCTION = True`
- [ ] 配置 `ALLOWED_HOSTS`
- [ ] 设置 `DEBUG = False`

## 后端部署

### ✅ 代码上传
- [ ] 上传后端代码到服务器
- [ ] 检查文件完整性
- [ ] 设置正确的文件权限

### ✅ 环境配置
- [ ] 创建Python虚拟环境
- [ ] 安装依赖包：`pip install -r requirements.txt`
- [ ] 安装Gunicorn：`pip install gunicorn`
- [ ] 设置环境变量

### ✅ 数据库迁移
- [ ] 运行 `python manage.py makemigrations`
- [ ] 运行 `python manage.py migrate`
- [ ] 创建超级用户（如需要）：`python manage.py createsuperuser`

### ✅ 静态文件
- [ ] 运行 `python manage.py collectstatic`
- [ ] 检查静态文件目录权限

### ✅ 服务配置
- [ ] 创建systemd服务文件
- [ ] 启用服务：`systemctl enable shiguangyinji-django`
- [ ] 启动服务：`systemctl start shiguangyinji-django`
- [ ] 检查服务状态：`systemctl status shiguangyinji-django`

## 前端部署

### ✅ 代码上传
- [ ] 上传前端代码到服务器
- [ ] 检查文件完整性

### ✅ 环境配置
- [ ] 安装Node.js依赖：`npm install`
- [ ] 创建生产环境配置文件 `.env.production`
- [ ] 设置正确的API基础URL

### ✅ 构建部署
- [ ] 运行构建命令：`npm run build`
- [ ] 检查构建输出目录 `dist/`
- [ ] 验证静态文件完整性

## Nginx配置

### ✅ 配置文件
- [ ] 创建Nginx配置文件
- [ ] 配置前端静态文件服务
- [ ] 配置后端API代理
- [ ] 配置媒体文件服务
- [ ] 测试配置文件：`nginx -t`

### ✅ 服务启动
- [ ] 启用站点配置
- [ ] 重启Nginx：`systemctl restart nginx`
- [ ] 检查Nginx状态：`systemctl status nginx`

## 功能测试

### ✅ 基础功能
- [ ] 访问前端页面：`http://101.37.28.188`
- [ ] 测试API接口：`http://101.37.28.188/api/`
- [ ] 检查静态资源加载
- [ ] 验证媒体文件访问

### ✅ 核心功能
- [ ] 用户注册/登录
- [ ] 文件上传功能
- [ ] 数字人生成
- [ ] 视频生成
- [ ] 数据库读写操作

### ✅ 性能测试
- [ ] 页面加载速度
- [ ] API响应时间
- [ ] 并发访问测试
- [ ] 内存使用情况

## 安全配置

### ✅ 基础安全
- [ ] 修改默认密码
- [ ] 配置防火墙规则
- [ ] 设置文件权限
- [ ] 禁用不必要的服务

### ✅ SSL证书（推荐）
- [ ] 申请SSL证书（Let's Encrypt）
- [ ] 配置HTTPS
- [ ] 设置HTTP重定向到HTTPS

## 监控和日志

### ✅ 日志配置
- [ ] 配置Django日志
- [ ] 配置Nginx日志
- [ ] 设置日志轮转
- [ ] 检查日志文件权限

### ✅ 监控设置
- [ ] 设置服务监控
- [ ] 配置错误告警
- [ ] 设置性能监控
- [ ] 配置备份策略

## 部署后验证

### ✅ 最终检查
- [ ] 所有服务正常运行
- [ ] 数据库连接正常
- [ ] 文件上传功能正常
- [ ] 用户功能正常
- [ ] 性能符合预期

### ✅ 文档更新
- [ ] 更新部署文档
- [ ] 记录配置变更
- [ ] 创建运维手册
- [ ] 设置故障处理流程

## 常见问题排查

### ❌ 服务无法启动
- [ ] 检查端口占用：`netstat -tlnp | grep :8000`
- [ ] 查看服务日志：`journalctl -u shiguangyinji-django -f`
- [ ] 检查配置文件语法
- [ ] 验证文件权限

### ❌ 数据库连接失败
- [ ] 检查数据库服务状态
- [ ] 验证连接参数
- [ ] 检查网络连通性
- [ ] 确认防火墙设置

### ❌ 前端无法访问
- [ ] 检查Nginx配置
- [ ] 验证静态文件路径
- [ ] 检查文件权限
- [ ] 查看Nginx错误日志

### ❌ 文件上传失败
- [ ] 检查媒体目录权限
- [ ] 验证磁盘空间
- [ ] 检查文件大小限制
- [ ] 查看上传日志

---

**检查清单版本：** v1.0  
**最后更新：** 2024年12月  
**使用说明：** 部署时请逐项检查，确保所有步骤都已完成 