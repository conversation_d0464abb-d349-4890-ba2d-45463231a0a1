#!/bin/bash

# 时光印记项目远程部署脚本
# 使用方法: ./deploy.sh *************

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查参数
if [ $# -eq 0 ]; then
    echo -e "${RED}错误: 请提供服务器IP地址${NC}"
    echo "使用方法: ./deploy.sh *************"
    exit 1
fi

SERVER_IP=$1
PROJECT_NAME="shiguangyinji"
REMOTE_USER="root"  # 根据实际情况修改

echo -e "${GREEN}开始部署时光印记项目到服务器: ${SERVER_IP}${NC}"

# 1. 检查本地文件
echo -e "${YELLOW}1. 检查本地项目文件...${NC}"
if [ ! -d "shiguangyinji_frontend-algorithm" ] || [ ! -d "shiguangyinji_backend-algorithm" ]; then
    echo -e "${RED}错误: 找不到前端或后端项目目录${NC}"
    exit 1
fi

# 2. 创建远程目录
echo -e "${YELLOW}2. 在服务器上创建项目目录...${NC}"
ssh ${REMOTE_USER}@${SERVER_IP} "mkdir -p /opt/${PROJECT_NAME}"

# 3. 上传后端代码
echo -e "${YELLOW}3. 上传后端代码...${NC}"
scp -r shiguangyinji_backend-algorithm/ ${REMOTE_USER}@${SERVER_IP}:/opt/${PROJECT_NAME}/

# 4. 上传前端代码
echo -e "${YELLOW}4. 上传前端代码...${NC}"
scp -r shiguangyinji_frontend-algorithm/ ${REMOTE_USER}@${SERVER_IP}:/opt/${PROJECT_NAME}/

# 5. 在服务器上执行部署命令
echo -e "${YELLOW}5. 在服务器上执行部署...${NC}"
ssh ${REMOTE_USER}@${SERVER_IP} << EOF
    set -e
    
    cd /opt/${PROJECT_NAME}
    
    # 安装系统依赖
    echo "安装系统依赖..."
    apt update
    apt install -y python3 python3-pip nodejs npm nginx mysql-client
    
    # 后端部署
    echo "部署后端..."
    cd shiguangyinji_backend-algorithm
    
    # 创建虚拟环境
    python3 -m venv venv
    source venv/bin/activate
    
    # 安装Python依赖
    pip install -r requirements.txt
    pip install gunicorn
    
    # 设置环境变量
    export DJANGO_SETTINGS_MODULE=shiguangyinji.settings
    export IS_PRODUCTION=True
    
    # 数据库迁移（需要手动配置数据库连接）
    echo "请手动配置数据库连接后运行迁移命令"
    # python manage.py makemigrations
    # python manage.py migrate
    
    # 收集静态文件
    python manage.py collectstatic --noinput
    
    # 创建systemd服务文件
    cat > /etc/systemd/system/${PROJECT_NAME}-django.service << 'SERVICE'
[Unit]
Description=Shiguangyinji Django Application
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/opt/${PROJECT_NAME}/shiguangyinji_backend-algorithm
Environment="PATH=/opt/${PROJECT_NAME}/shiguangyinji_backend-algorithm/venv/bin"
Environment="DJANGO_SETTINGS_MODULE=shiguangyinji.settings"
Environment="IS_PRODUCTION=True"
ExecStart=/opt/${PROJECT_NAME}/shiguangyinji_backend-algorithm/venv/bin/gunicorn --bind 0.0.0.0:8000 shiguangyinji.wsgi:application
Restart=always

[Install]
WantedBy=multi-user.target
SERVICE
    
    # 前端部署
    echo "部署前端..."
    cd ../shiguangyinji_frontend-algorithm
    
    # 安装Node.js依赖
    npm install
    
    # 创建生产环境配置
    echo "VITE_API_BASE_URL=http://${SERVER_IP}:8000/api/" > .env.production
    
    # 构建前端
    npm run build
    
    # 配置Nginx
    cat > /etc/nginx/sites-available/${PROJECT_NAME} << 'NGINX'
server {
    listen 80;
    server_name ${SERVER_IP};
    
    # 前端静态文件
    location / {
        root /opt/${PROJECT_NAME}/shiguangyinji_frontend-algorithm/dist;
        try_files \$uri \$uri/ /index.html;
    }
    
    # 后端API代理
    location /api/ {
        proxy_pass http://127.0.0.1:8000/api/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    }
    
    # 媒体文件
    location /media/ {
        alias /opt/${PROJECT_NAME}/shiguangyinji_backend-algorithm/media/;
    }
}
NGINX
    
    # 启用Nginx配置
    ln -sf /etc/nginx/sites-available/${PROJECT_NAME} /etc/nginx/sites-enabled/
    nginx -t
    systemctl restart nginx
    
    # 启动Django服务
    systemctl daemon-reload
    systemctl enable ${PROJECT_NAME}-django
    systemctl start ${PROJECT_NAME}-django
    
    # 设置文件权限
    chown -R www-data:www-data /opt/${PROJECT_NAME}
    
    echo "部署完成！"
EOF

echo -e "${GREEN}部署完成！${NC}"
echo -e "${YELLOW}请访问: http://${SERVER_IP}${NC}"
echo -e "${YELLOW}注意: 请手动配置数据库连接信息${NC}"
 